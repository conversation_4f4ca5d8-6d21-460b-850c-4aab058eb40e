import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/views/MainLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'main',
      component: MainLayout,
      redirect: '/dashboard',
      children: [
        // 仪表盘
        {
          path: '/dashboard',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue'),
          meta: { title: '仪表盘', icon: 'Monitor' }
        },
        
        // 服务台模块
        {
          path: '/service-desk',
          name: 'ServiceDesk',
          component: () => import('@/views/ServiceDesk/ServiceDesk.vue'),
          meta: { title: '服务台', icon: 'Headset' }
        },
        {
          path: '/service-desk/tickets',
          name: 'ServiceDeskTickets',
          component: () => import('@/views/ServiceDesk/Tickets.vue'),
          meta: { title: '工单管理', icon: 'Document' }
        },
        {
          path: '/service-desk/status',
          name: 'ServiceDeskStatus',
          component: () => import('@/views/ServiceDesk/StatusBoard.vue'),
          meta: { title: '服务状态看板', icon: 'DataBoard' }
        },

        // 事件管理
        {
          path: '/incident',
          name: 'IncidentManagement',
          component: () => import('@/views/Incident/IncidentManagement.vue'),
          meta: { title: '事件管理', icon: 'Warning' }
        },
        {
          path: '/incident/list',
          name: 'IncidentList',
          component: () => import('@/views/Incident/IncidentList.vue'),
          meta: { title: '事件列表', icon: 'List' }
        },
        {
          path: '/incident/escalation',
          name: 'IncidentEscalation',
          component: () => import('@/views/Incident/EscalationManagement.vue'),
          meta: { title: '升级管理', icon: 'Top' }
        },

        // 问题管理
        {
          path: '/problem',
          name: 'ProblemManagement',
          component: () => import('@/views/Problem/ProblemManagement.vue'),
          meta: { title: '问题管理', icon: 'QuestionFilled' }
        },
        {
          path: '/problem/rca',
          name: 'RootCauseAnalysis',
          component: () => import('@/views/Problem/RootCauseAnalysis.vue'),
          meta: { title: '根本原因分析', icon: 'Search' }
        },
        {
          path: '/problem/kedb',
          name: 'KnownErrorDB',
          component: () => import('@/views/Problem/KnownErrorDB.vue'),
          meta: { title: '已知错误库', icon: 'Collection' }
        },

        // 变更管理
        {
          path: '/change',
          name: 'ChangeManagement',
          component: () => import('@/views/Change/ChangeManagement.vue'),
          meta: { title: '变更管理', icon: 'Edit' }
        },
        {
          path: '/change/calendar',
          name: 'ChangeCalendar',
          component: () => import('@/views/Change/ChangeCalendar.vue'),
          meta: { title: '变更日历', icon: 'Calendar' }
        },
        {
          path: '/change/cab',
          name: 'CABApproval',
          component: () => import('@/views/Change/CABApproval.vue'),
          meta: { title: 'CAB审批', icon: 'Select' }
        },

        // CMDB配置管理
        {
          path: '/cmdb',
          name: 'CMDB',
          component: () => import('@/views/CMDB/CMDB.vue'),
          meta: { title: 'CMDB', icon: 'Box' }
        },
        {
          path: '/cmdb/topology',
          name: 'CMDBTopology',
          component: () => import('@/views/CMDB/Topology.vue'),
          meta: { title: '拓扑图', icon: 'Grid' }
        },
        {
          path: '/cmdb/discovery',
          name: 'CMDBDiscovery',
          component: () => import('@/views/CMDB/AutoDiscovery.vue'),
          meta: { title: '自动发现', icon: 'Refresh' }
        },
        {
          path: '/cmdb/graph',
          name: 'CIGraph',
          component: () => import('@/views/CMDB/CIGraph.vue'),
          meta: { title: '配置项图谱', icon: 'Link' }
        },
        {
          path: '/cmdb/monitor',
          name: 'TopologyMonitor',
          component: () => import('@/views/CMDB/TopologyMonitor.vue'),
          meta: { title: '拓扑监控', icon: 'Monitor' }
        },
        {
          path: '/cmdb/test',
          name: 'CMDBTest',
          component: () => import('@/views/CMDB/CMDBTest.vue'),
          meta: { title: 'CMDB测试', icon: 'Tools' }
        },
        {
          path: '/cmdb/ci/:id',
          name: 'CIDetails',
          component: () => import('@/views/CMDB/CIDetails.vue'),
          meta: { title: 'CI详情', icon: 'Document' }
        },
        {
          path: '/cmdb/quality',
          name: 'DataQuality',
          component: () => import('@/views/CMDB/DataQuality.vue'),
          meta: { title: '数据质量', icon: 'TrendCharts' }
        },
        {
          path: '/cmdb/relations',
          name: 'RelationMapping',
          component: () => import('@/views/CMDB/RelationMapping.vue'),
          meta: { title: '关系映射', icon: 'Link' }
        },
        {
          path: '/cmdb/versions',
          name: 'VersionControl',
          component: () => import('@/views/CMDB/VersionControl.vue'),
          meta: { title: '版本控制', icon: 'Document' }
        },

        // 发布管理
        {
          path: '/release',
          name: 'ReleaseManagement',
          component: () => import('@/views/Release/ReleaseManagement.vue'),
          meta: { title: '发布管理', icon: 'Upload' }
        },
        {
          path: '/release/dashboard',
          name: 'ReleaseDashboard',
          component: () => import('@/views/Release/ReleaseDashboard.vue'),
          meta: { title: '发布看板', icon: 'DataBoard' }
        },
        {
          path: '/release/detail/:id',
          name: 'ReleaseDetail',
          component: () => import('@/views/Release/ReleaseDetail.vue'),
          meta: { title: '发布详情', icon: 'Document' }
        },
        {
          path: '/release/calendar',
          name: 'ReleaseCalendarView',
          component: () => import('@/views/Release/ReleaseCalendarView.vue'),
          meta: { title: '发布日历', icon: 'Calendar' }
        },
        {
          path: '/release/reports',
          name: 'ReleaseReportsView',
          component: () => import('@/views/Release/ReleaseReportsView.vue'),
          meta: { title: '发布报表', icon: 'TrendCharts' }
        },
        {
          path: '/release/test',
          name: 'ReleaseTest',
          component: () => import('@/views/Release/ReleaseTest.vue'),
          meta: { title: '功能测试', icon: 'Tools' }
        },
        {
          path: '/release/api-test',
          name: 'ApiTest',
          component: () => import('@/views/Release/ApiTest.vue'),
          meta: { title: 'API测试', icon: 'Monitor' }
        },

        // 服务级别管理
        {
          path: '/slm',
          name: 'ServiceLevelManagement',
          component: () => import('@/views/SLM/ServiceLevelManagement.vue'),
          meta: { title: '服务级别管理', icon: 'TrendCharts' }
        },
        {
          path: '/slm/reports',
          name: 'SLAReports',
          component: () => import('@/views/SLM/SLAReports.vue'),
          meta: { title: 'SLA报告', icon: 'Document' }
        },
        {
          path: '/slm/agreements',
          name: 'AgreementManagement',
          component: () => import('@/views/SLM/AgreementManagement.vue'),
          meta: { title: '协议管理', icon: 'Files' }
        },
        {
          path: '/slm/monitoring',
          name: 'SLAMonitoring',
          component: () => import('@/views/SLM/SLAMonitoring.vue'),
          meta: { title: 'SLA监控', icon: 'Monitor' }
        },
        {
          path: '/slm/health',
          name: 'ServiceHealth',
          component: () => import('@/views/SLM/ServiceHealth.vue'),
          meta: { title: '服务健康', icon: 'TrendCharts' }
        },
        {
          path: '/slm/test',
          name: 'SLMTest',
          component: () => import('@/views/SLM/SLMTest.vue'),
          meta: { title: 'SLM测试', icon: 'Tools' }
        },

        // 知识管理
        {
          path: '/knowledge',
          name: 'KnowledgeManagement',
          component: () => import('@/views/Knowledge/KnowledgeManagement.vue'),
          meta: { title: '知识管理', icon: 'Reading' }
        },
        {
          path: '/knowledge/base',
          name: 'KnowledgeBase',
          component: () => import('@/views/Knowledge/KnowledgeBase.vue'),
          meta: { title: '知识库', icon: 'Collection' }
        },

        // 资产管理
        {
          path: '/asset',
          name: 'AssetManagement',
          component: () => import('@/views/Asset/AssetManagement.vue'),
          meta: { title: '资产管理', icon: 'Box' }
        },
        {
          path: '/asset/lifecycle',
          name: 'AssetLifecycle',
          component: () => import('@/views/Asset/AssetLifecycle.vue'),
          meta: { title: '资产生命周期', icon: 'Timer' }
        },
        {
          path: '/asset/license',
          name: 'LicenseManagement',
          component: () => import('@/views/Asset/LicenseManagement.vue'),
          meta: { title: '许可证管理', icon: 'Key' }
        },

        // 请求履行
        {
          path: '/request',
          name: 'RequestFulfillment',
          component: () => import('@/views/Request/RequestFulfillment.vue'),
          meta: { title: '请求履行', icon: 'Service' }
        },
        {
          path: '/request/catalog',
          name: 'ServiceCatalog',
          component: () => import('@/views/Request/ServiceCatalog.vue'),
          meta: { title: '服务目录', icon: 'Menu' }
        },
        {
          path: '/request/portal',
          name: 'SelfServicePortal',
          component: () => import('@/views/Request/SelfServicePortal.vue'),
          meta: { title: '自助服务门户', icon: 'User' }
        },

        // 持续改进
        {
          path: '/csi',
          name: 'ContinualImprovement',
          component: () => import('@/views/CSI/ContinualImprovement.vue'),
          meta: { title: '持续改进', icon: 'TrendCharts' }
        },
        {
          path: '/csi/register',
          name: 'CSIRegister',
          component: () => import('@/views/CSI/CSIRegister.vue'),
          meta: { title: 'CSI登记册', icon: 'Document' }
        },

        // 报表中心
        {
          path: '/reports',
          name: 'ReportsCenter',
          component: () => import('@/views/Reports/ReportsCenter.vue'),
          meta: { title: '报表中心', icon: 'TrendCharts' }
        },

        // 系统设置
        {
          path: '/settings',
          name: 'SystemSettings',
          component: () => import('@/views/Settings/SystemSettings.vue'),
          meta: { title: '系统设置', icon: 'Setting' }
        },

        // 测试页面
        {
          path: '/test',
          name: 'TestPage',
          component: () => import('@/views/TestPage.vue'),
          meta: { title: '测试页面', icon: 'Document' }
        },

        // 事件管理测试页面
        {
          path: '/incident-test',
          name: 'IncidentTestPage',
          component: () => import('@/views/IncidentTestPage.vue'),
          meta: { title: '事件管理测试', icon: 'Tools' }
        }
      ]
    },
    
    // 登录页面
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { title: '登录' }
    },
    
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue'),
      meta: { title: '页面未找到' }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - ITSM服务管理平台`
  }
  next()
})

export default router
