<template>
  <div class="main-layout">
    <!-- 顶部状态栏 -->
    <header class="layout-header">
      <div class="header-left">
        <el-button
          type="text"
          @click="toggleSidebar"
          class="sidebar-toggle"
          :class="{ 'is-active': !sidebarCollapsed }"
        >
          <el-icon :size="20">
            <Menu />
          </el-icon>
        </el-button>
        <div class="logo">
          <el-icon :size="24" color="#1976D2">
            <Monitor />
          </el-icon>
          <span v-show="!sidebarCollapsed" class="logo-text">ITSM服务管理平台</span>
        </div>
      </div>

      <div class="header-right">
        <!-- 全局搜索 -->
        <div class="global-search">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索工单、知识库..."
            prefix-icon="Search"
            size="small"
            style="width: 250px;"
            @keyup.enter="handleSearch"
          />
        </div>

        <!-- 通知中心 -->
        <el-dropdown @command="handleNotificationCommand">
          <el-button type="text" class="header-btn">
            <el-badge :value="notificationCount" :hidden="notificationCount === 0">
              <el-icon :size="18">
                <Bell />
              </el-icon>
            </el-badge>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="viewAll">查看所有通知</el-dropdown-item>
              <el-dropdown-item command="markRead">标记已读</el-dropdown-item>
              <el-dropdown-item command="settings">通知设置</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 消息中心 -->
        <el-button type="text" class="header-btn" @click="showMessages">
          <el-badge :value="messageCount" :hidden="messageCount === 0">
            <el-icon :size="18">
              <ChatDotRound />
            </el-icon>
          </el-badge>
        </el-button>

        <!-- 快捷设置 -->
        <el-dropdown @command="handleSettingsCommand">
          <el-button type="text" class="header-btn">
            <el-icon :size="18">
              <Setting />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="theme">主题设置</el-dropdown-item>
              <el-dropdown-item command="language">语言设置</el-dropdown-item>
              <el-dropdown-item command="shortcuts">快捷键</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand">
          <div class="user-info">
            <el-avatar :size="32" :src="userInfo.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div v-show="!sidebarCollapsed" class="user-details">
              <div class="user-name">{{ userInfo.name }}</div>
              <div class="user-role">{{ userInfo.role }}</div>
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="preferences">偏好设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <div class="layout-container">
      <!-- 左侧导航栏 -->
      <aside class="layout-sidebar" :class="{ 'is-collapsed': sidebarCollapsed }">
        <el-scrollbar class="sidebar-scrollbar">
          <el-menu
            :default-active="activeMenu"
            :collapse="sidebarCollapsed"
            :unique-opened="true"
            router
            class="sidebar-menu"
          >
            <!-- 仪表盘 -->
            <el-menu-item index="/dashboard">
              <el-icon><Monitor /></el-icon>
              <template #title>仪表盘</template>
            </el-menu-item>

            <!-- 服务台 -->
            <el-sub-menu index="service-desk">
              <template #title>
                <el-icon><Headset /></el-icon>
                <span>服务台</span>
              </template>
              <el-menu-item index="/service-desk">服务台概览</el-menu-item>
              <el-menu-item index="/service-desk/tickets">工单管理</el-menu-item>
              <el-menu-item index="/service-desk/status">状态看板</el-menu-item>
            </el-sub-menu>

            <!-- 事件管理 -->
            <el-sub-menu index="incident">
              <template #title>
                <el-icon><Warning /></el-icon>
                <span>事件管理</span>
              </template>
              <el-menu-item index="/incident">事件概览</el-menu-item>
              <el-menu-item index="/incident/list">事件列表</el-menu-item>
              <el-menu-item index="/incident/escalation">升级管理</el-menu-item>
            </el-sub-menu>

            <!-- 问题管理 -->
            <el-sub-menu index="problem">
              <template #title>
                <el-icon><QuestionFilled /></el-icon>
                <span>问题管理</span>
              </template>
              <el-menu-item index="/problem">问题概览</el-menu-item>
              <el-menu-item index="/problem/rca">根因分析</el-menu-item>
              <el-menu-item index="/problem/kedb">已知错误库</el-menu-item>
            </el-sub-menu>

            <!-- 变更管理 -->
            <el-sub-menu index="change">
              <template #title>
                <el-icon><Edit /></el-icon>
                <span>变更管理</span>
              </template>
              <el-menu-item index="/change">变更概览</el-menu-item>
              <el-menu-item index="/change/calendar">变更日历</el-menu-item>
              <el-menu-item index="/change/cab">CAB审批</el-menu-item>
            </el-sub-menu>

            <!-- CMDB -->
            <el-sub-menu index="cmdb">
              <template #title>
                <el-icon><Box /></el-icon>
                <span>配置管理</span>
              </template>
              <el-menu-item index="/cmdb">CMDB概览</el-menu-item>
              <el-menu-item index="/cmdb/graph">配置项图谱</el-menu-item>
              <el-menu-item index="/cmdb/topology">拓扑图</el-menu-item>
              <el-menu-item index="/cmdb/monitor">拓扑监控</el-menu-item>
              <el-menu-item index="/cmdb/discovery">自动发现</el-menu-item>
            </el-sub-menu>

            <!-- 发布管理 -->
            <el-sub-menu index="release">
              <template #title>
                <el-icon><Upload /></el-icon>
                <span>发布管理</span>
              </template>
              <el-menu-item index="/release">发布概览</el-menu-item>
              <el-menu-item index="/release/dashboard">发布看板</el-menu-item>
            </el-sub-menu>

            <!-- 服务级别管理 -->
            <el-sub-menu index="slm">
              <template #title>
                <el-icon><TrendCharts /></el-icon>
                <span>服务级别管理</span>
              </template>
              <el-menu-item index="/slm">SLM概览</el-menu-item>
              <el-menu-item index="/slm/reports">SLA报告</el-menu-item>
            </el-sub-menu>

            <!-- 知识管理 -->
            <el-sub-menu index="knowledge">
              <template #title>
                <el-icon><Reading /></el-icon>
                <span>知识管理</span>
              </template>
              <el-menu-item index="/knowledge">知识概览</el-menu-item>
              <el-menu-item index="/knowledge/base">知识库</el-menu-item>
            </el-sub-menu>

            <!-- 资产管理 -->
            <el-sub-menu index="asset">
              <template #title>
                <el-icon><Box /></el-icon>
                <span>资产管理</span>
              </template>
              <el-menu-item index="/asset">资产概览</el-menu-item>
              <el-menu-item index="/asset/lifecycle">生命周期</el-menu-item>
              <el-menu-item index="/asset/license">许可证管理</el-menu-item>
            </el-sub-menu>

            <!-- 请求履行 -->
            <el-sub-menu index="request">
              <template #title>
                <el-icon><Service /></el-icon>
                <span>请求履行</span>
              </template>
              <el-menu-item index="/request">请求概览</el-menu-item>
              <el-menu-item index="/request/catalog">服务目录</el-menu-item>
              <el-menu-item index="/request/portal">自助门户</el-menu-item>
            </el-sub-menu>

            <!-- 持续改进 -->
            <el-sub-menu index="csi">
              <template #title>
                <el-icon><TrendCharts /></el-icon>
                <span>持续改进</span>
              </template>
              <el-menu-item index="/csi">CSI概览</el-menu-item>
              <el-menu-item index="/csi/register">CSI登记册</el-menu-item>
            </el-sub-menu>

            <!-- 报表中心 -->
            <el-menu-item index="/reports">
              <el-icon><DataAnalysis /></el-icon>
              <template #title>报表中心</template>
            </el-menu-item>

            <!-- 系统设置 -->
            <el-menu-item index="/settings">
              <el-icon><Setting /></el-icon>
              <template #title>系统设置</template>
            </el-menu-item>
          </el-menu>
        </el-scrollbar>

        <!-- 侧边栏底部 -->
        <div class="sidebar-footer">
          <div v-show="!sidebarCollapsed" class="footer-info">
            <div class="system-status">
              <el-icon :size="12" color="#4CAF50">
                <CircleCheckFilled />
              </el-icon>
              <span>系统正常</span>
            </div>
            <div class="version-info">v2.1.0</div>
          </div>
        </div>
      </aside>

      <!-- 主内容区 -->
      <main class="layout-main">
        <div class="main-content">
          <router-view />
        </div>
      </main>
    </div>

    <!-- 用户体验增强组件 -->
    <UXEnhancer ref="uxEnhancer" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import UXEnhancer from '@/components/common/UXEnhancer.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const sidebarCollapsed = ref(false)
const searchKeyword = ref('')
const notificationCount = ref(5)
const messageCount = ref(3)

// 用户信息
const userInfo = ref({
  name: '管理员',
  role: 'IT主管',
  avatar: ''
})

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存到本地存储
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
}

// 全局搜索
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim()
    ElMessage.success(`正在搜索: ${keyword}`)

    // 实现全局搜索逻辑
    // 可以搜索工单、知识库、配置项等
    const searchResults = performGlobalSearch(keyword)

    if (searchResults.length > 0) {
      // 如果有搜索结果，可以显示搜索结果页面或下拉列表
      showSearchResults(searchResults)
    } else {
      ElMessage.warning('未找到相关内容')
    }
  } else {
    ElMessage.warning('请输入搜索关键词')
  }
}

// 执行全局搜索
const performGlobalSearch = (keyword) => {
  // 模拟搜索结果
  const mockResults = [
    { type: 'ticket', id: 'T-001', title: `包含"${keyword}"的工单`, url: '/service-desk/tickets' },
    { type: 'knowledge', id: 'KB-001', title: `包含"${keyword}"的知识库文章`, url: '/knowledge/base' },
    { type: 'ci', id: 'CI-001', title: `包含"${keyword}"的配置项`, url: '/cmdb' }
  ]

  return mockResults.filter(item =>
    item.title.toLowerCase().includes(keyword.toLowerCase())
  )
}

// 显示搜索结果
const showSearchResults = (results) => {
  // 这里可以实现搜索结果的显示逻辑
  // 比如跳转到搜索结果页面或显示下拉菜单
  console.log('搜索结果:', results)
}

// 通知命令处理
const handleNotificationCommand = (command) => {
  switch (command) {
    case 'viewAll':
      // 跳转到通知中心页面
      router.push('/notifications')
      ElMessage.info('跳转到通知中心')
      break
    case 'markRead':
      // 标记所有通知为已读
      markAllNotificationsAsRead()
      break
    case 'settings':
      // 打开通知设置
      openNotificationSettings()
      break
  }
}

// 标记所有通知为已读
const markAllNotificationsAsRead = () => {
  notificationCount.value = 0
  ElMessage.success('所有通知已标记为已读')
  // 这里可以调用API标记通知为已读
}

// 打开通知设置
const openNotificationSettings = () => {
  ElMessageBox.confirm(
    '通知设置功能包括：\n• 邮件通知开关\n• 短信通知开关\n• 浏览器推送通知\n• 通知频率设置',
    '通知设置',
    {
      confirmButtonText: '打开设置',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    // 这里可以打开通知设置对话框或跳转到设置页面
    router.push('/settings')
    ElMessage.info('跳转到系统设置')
  }).catch(() => {
    // 用户取消
  })
}

// 显示消息
const showMessages = () => {
  ElMessage.info('消息中心')
}

// 设置命令处理
const handleSettingsCommand = (command) => {
  switch (command) {
    case 'theme':
      openThemeSettings()
      break
    case 'language':
      openLanguageSettings()
      break
    case 'shortcuts':
      openShortcutSettings()
      break
  }
}

// 打开主题设置
const openThemeSettings = () => {
  ElMessageBox.confirm(
    '主题设置功能包括：\n• 浅色/深色主题切换\n• 主题色彩自定义\n• 字体大小调整',
    '主题设置',
    {
      confirmButtonText: '打开设置',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    ElMessage.info('主题设置功能开发中...')
  })
}

// 打开语言设置
const openLanguageSettings = () => {
  ElMessageBox.confirm(
    '语言设置功能包括：\n• 中文/英文切换\n• 时区设置\n• 日期格式设置',
    '语言设置',
    {
      confirmButtonText: '打开设置',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    ElMessage.info('语言设置功能开发中...')
  })
}

// 打开快捷键设置
const openShortcutSettings = () => {
  ElMessageBox.alert(
    '常用快捷键：\n• Ctrl+N: 新建工单\n• Ctrl+F: 全局搜索\n• Ctrl+D: 打开仪表盘\n• F1: 帮助中心',
    '快捷键说明',
    {
      confirmButtonText: '确定',
      type: 'info',
    }
  )
}

// 用户命令处理
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料')
      break
    case 'preferences':
      ElMessage.info('偏好设置')
      break
    case 'logout':
      ElMessage.success('已退出登录')
      router.push('/login')
      break
  }
}

// 监听路由变化，自动收起移动端侧边栏
watch(route, () => {
  if (window.innerWidth <= 768) {
    sidebarCollapsed.value = true
  }
})

// 监听窗口大小变化
const handleResize = () => {
  if (window.innerWidth <= 768) {
    sidebarCollapsed.value = true
  } else {
    // 恢复桌面端的侧边栏状态
    const saved = localStorage.getItem('sidebarCollapsed')
    if (saved !== null) {
      sidebarCollapsed.value = saved === 'true'
    }
  }
}

onMounted(() => {
  // 恢复侧边栏状态
  const saved = localStorage.getItem('sidebarCollapsed')
  if (saved !== null) {
    sidebarCollapsed.value = saved === 'true'
  }

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
  handleResize() // 初始检查
})
</script>

<style scoped>
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

/* 顶部状态栏 */
.layout-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.sidebar-toggle:hover {
  background-color: #f0f2f5;
}

.sidebar-toggle.is-active {
  background-color: #e6f7ff;
  color: #1976D2;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1976D2;
}

.logo-text {
  transition: all 0.3s;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.global-search {
  margin-right: 8px;
}

.header-btn {
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.header-btn:hover {
  background-color: #f0f2f5;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f0f2f5;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

/* 布局容器 */
.layout-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧导航栏 */
.layout-sidebar {
  width: 240px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  transition: width 0.3s;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.layout-sidebar.is-collapsed {
  width: 64px;
}

.sidebar-scrollbar {
  flex: 1;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.sidebar-menu .el-menu-item,
.sidebar-menu .el-sub-menu__title {
  height: 48px;
  line-height: 48px;
  margin: 2px 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-sub-menu__title:hover {
  background-color: #f0f2f5;
  color: #1976D2;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #e6f7ff;
  color: #1976D2;
  font-weight: 500;
}

.sidebar-menu .el-menu-item.is-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1976D2;
  border-radius: 0 2px 2px 0;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #4CAF50;
}

.version-info {
  font-size: 11px;
  color: #999;
  text-align: center;
}

/* 主内容区 */
.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow: auto;
  background: #f5f7fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px;
  }

  .header-left .logo-text {
    display: none;
  }

  .global-search {
    display: none;
  }

  .user-details {
    display: none;
  }

  .layout-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    bottom: 0;
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }

  .layout-sidebar:not(.is-collapsed) {
    transform: translateX(0);
  }

  .layout-main {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .header-right {
    gap: 8px;
  }

  .header-btn {
    padding: 6px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .main-layout {
    background-color: #1a1a1a;
  }

  .layout-header {
    background: #2d2d2d;
    border-bottom-color: #404040;
    color: #fff;
  }

  .layout-sidebar {
    background: #2d2d2d;
    border-right-color: #404040;
  }

  .sidebar-menu .el-menu-item,
  .sidebar-menu .el-sub-menu__title {
    color: #e0e0e0;
  }

  .sidebar-menu .el-menu-item:hover,
  .sidebar-menu .el-sub-menu__title:hover {
    background-color: #404040;
    color: #1976D2;
  }

  .sidebar-menu .el-menu-item.is-active {
    background-color: #1976D2;
    color: #fff;
  }

  .main-content {
    background: #1a1a1a;
  }
}

/* 滚动条样式 */
:deep(.el-scrollbar__bar) {
  opacity: 0.3;
}

:deep(.el-scrollbar__bar:hover) {
  opacity: 0.6;
}

/* 菜单图标对齐 */
:deep(.el-menu-item .el-icon),
:deep(.el-sub-menu__title .el-icon) {
  margin-right: 8px;
  width: 20px;
  text-align: center;
}

/* 子菜单样式 */
:deep(.el-sub-menu .el-menu-item) {
  padding-left: 48px !important;
  font-size: 13px;
}

:deep(.el-sub-menu .el-menu-item:hover) {
  background-color: #f0f2f5;
}

:deep(.el-sub-menu .el-menu-item.is-active) {
  background-color: #e6f7ff;
  color: #1976D2;
  font-weight: 500;
}
</style>