<template>
  <div class="knowledge-base">
    <div class="page-header">
      <h2>知识库</h2>
      <p>浏览和搜索知识库内容</p>
    </div>
    <el-card>
      <div class="placeholder">
        <p>知识库功能开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
</script>

<style scoped>
.knowledge-base {
  padding: 20px;
}
.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}
.page-header p {
  color: #616161;
  margin: 0 0 20px 0;
}
.placeholder {
  text-align: center;
  padding: 40px;
  color: #666;
}
</style>
